import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:flutter_loadingindicator/flutter_loadingindicator.dart';

import '../routes/go_router_config.dart';
import '../routes/app_pages.dart';
import '../services/navigation_service.dart';
import '../routes/route_names.dart';

/// Hybrid app that supports both GetX and go_router
/// This allows gradual migration from GetX to go_router
class HybridApp extends StatefulWidget {
  final String initialRoute;
  final FirebaseAnalytics analytics;

  const HybridApp({
    super.key,
    required this.initialRoute,
    required this.analytics,
  });

  @override
  State<HybridApp> createState() => _HybridAppState();
}

class _HybridAppState extends State<HybridApp> {
  late final GoRouter _goRouter;
  late final NavigationService _navigationService;

  @override
  void initState() {
    super.initState();

    debugPrint('=== HYBRID_APP: Initializing HybridApp ===');
    debugPrint('HYBRID_APP: Initial route: ${widget.initialRoute}');

    // Initialize go_router
    debugPrint('HYBRID_APP: Creating go_router...');
    _goRouter = GoRouterConfig.createRouter();
    debugPrint('HYBRID_APP: go_router created successfully');

    // Initialize navigation service
    debugPrint('HYBRID_APP: Setting up NavigationService...');
    _navigationService = Get.put(NavigationService());
    _navigationService.setGoRouter(_goRouter);
    debugPrint('HYBRID_APP: NavigationService set up successfully');

    debugPrint('HYBRID_APP: Initialization completed');
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('=== HYBRID_APP: Building app ===');
    debugPrint('HYBRID_APP: Initial route: ${widget.initialRoute}');

    // Check if initial route should use go_router
    final isMigratedToGoRouter =
        RouteNames.isMigratedToGoRouter(widget.initialRoute);
    debugPrint(
        'HYBRID_APP: Route is migrated to go_router: $isMigratedToGoRouter');

    if (isMigratedToGoRouter) {
      debugPrint('HYBRID_APP: Building go_router app');
      return _buildGoRouterApp();
    } else {
      debugPrint('HYBRID_APP: Building GetX app');
      return _buildGetXApp();
    }
  }

  /// Build app with go_router for migrated routes
  Widget _buildGoRouterApp() {
    debugPrint('HYBRID_APP: Creating MaterialApp.router with go_router');
    debugPrint(
        'HYBRID_APP: go_router initial location: ${_goRouter.routerDelegate.currentConfiguration}');

    return MaterialApp.router(
      debugShowCheckedModeBanner: false,
      title: "Automoment",
      routerConfig: _goRouter,
      builder: (context, child) {
        debugPrint('HYBRID_APP: MaterialApp.router builder called');
        return EasyLoading.init()(context, child);
      },
    );
  }

  /// Build app with GetX for non-migrated routes
  Widget _buildGetXApp() {
    debugPrint('HYBRID_APP: Creating GetMaterialApp with GetX');
    debugPrint('HYBRID_APP: GetX initial route: ${widget.initialRoute}');
    debugPrint('HYBRID_APP: Available GetX routes: ${AppPages.routes.length}');

    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: "Automoment",
      initialRoute: widget.initialRoute,
      getPages: AppPages.routes,
      builder: (context, child) {
        debugPrint('HYBRID_APP: GetMaterialApp builder called');
        return EasyLoading.init()(context, child);
      },
      navigatorKey: GoRouterConfig.rootNavigatorKey,
      navigatorObservers: [
        FirebaseAnalyticsObserver(analytics: widget.analytics),
        SentryNavigatorObserver(),
      ],
      onGenerateRoute: (settings) {
        debugPrint('HYBRID_APP: onGenerateRoute called for: ${settings.name}');

        // Check if this route should be handled by go_router
        if (RouteNames.isMigratedToGoRouter(settings.name ?? '')) {
          debugPrint(
              'HYBRID_APP: Route ${settings.name} should be handled by go_router, redirecting...');

          // Navigate to go_router
          WidgetsBinding.instance.addPostFrameCallback((_) {
            debugPrint(
                'HYBRID_APP: Navigating to go_router route: ${settings.name}');
            _goRouter.go(settings.name!);
          });

          // Return a temporary loading page
          return MaterialPageRoute(
            builder: (context) => const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }
        debugPrint(
            'HYBRID_APP: Route ${settings.name} will be handled by GetX');
        return null;
      },
    );
  }
}

/// Custom route information parser for hybrid routing
class HybridRouteInformationParser
    extends RouteInformationParser<RouteInformation> {
  @override
  Future<RouteInformation> parseRouteInformation(
      RouteInformation routeInformation) async {
    return routeInformation;
  }

  @override
  RouteInformation restoreRouteInformation(RouteInformation configuration) {
    return configuration;
  }
}

/// Custom router delegate for hybrid routing
class HybridRouterDelegate extends RouterDelegate<RouteInformation>
    with ChangeNotifier, PopNavigatorRouterDelegateMixin<RouteInformation> {
  @override
  GlobalKey<NavigatorState> get navigatorKey => GoRouterConfig.rootNavigatorKey;

  @override
  RouteInformation? get currentConfiguration {
    // Return current route information
    return RouteInformation(
      uri: Uri.parse(Get.currentRoute),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Navigator(
      key: navigatorKey,
      pages: [
        MaterialPage(
          child: Container(), // This will be handled by GetX or go_router
        ),
      ],
      onDidRemovePage: (page) {
        // Handle page removal
      },
    );
  }

  @override
  Future<void> setNewRoutePath(RouteInformation configuration) async {
    // Handle route changes
    final location = configuration.uri.path;

    if (RouteNames.isMigratedToGoRouter(location)) {
      // Let go_router handle this
      return;
    } else {
      // Let GetX handle this
      Get.toNamed(location);
    }
  }
}
