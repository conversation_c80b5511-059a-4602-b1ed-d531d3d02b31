/// Shared route names for both GetX and go_router
/// This ensures consistency during the migration process
abstract class RouteNames {
  RouteNames._();

  // Routes migrated to go_router
  static const String login = '/login';
  static const String profileEdit = '/profile-edit';
  static const String navigationTest = '/navigation-test';
  static const String register = '/register';
  static const String forgotPasswordEmail = '/forgot-password-email';
  static const String forgotPasswordCheckCode = '/forgot-password-check-code';
  static const String forgotPasswordReset = '/forgot-password-reset';
  static const String socialLoginForm = '/social-login-form';

  // Routes still using GetX (reference from existing app_routes.dart)
  static const String home = '/home';
  static const String bottombar = '/';
  static const String news = '/news';
  static const String events = '/events';
  static const String account = '/account';
  static const String guest = '/guest';
  static const String homeDetail = '/home-detail';
  static const String eventsDetail = '/events-detail';
  static const String booking = '/booking';
  static const String checkIn = '/check-in';
  static const String bookingSuccess = '/booking-success';

  static const String eventsForm = '/events-form';
  static const String eventsFormSuccess = '/events-form-success';
  static const String eventsFormSigned = '/events-form-signed';
  static const String eventsFormUpdate = '/events-form-update';
  static const String eventsFormUpdateSuccess = '/events-form-update-success';
  static const String vehicles = '/vehicles';
  static const String vehiclesAdd = '/vehicles-add';
  static const String vehiclesEdit = '/vehicles-edit';
  static const String kconfirmDialog = '/kconfirm-dialog';
  static const String notification = '/notification';
  static const String notificationDetail = '/notification-detail';
  static const String eventsFormSign = '/events-form-sign';
  static const String eventsFormSignSuccess = '/events-form-sign-success';
  static const String accountDelete = '/account-delete';
  static const String verifyEmail = '/verify-email';
  static const String chatList = '/chat-list';
  static const String chat = '/chat';
  static const String leaderboard = '/leaderboard';
  static const String personalResults = '/personal-results';
  static const String leaderboardDetail = '/leaderboard-detail';
  static const String forceUpgrade = '/force-upgrade';
  static const String perlaps = '/perlaps';
  static const String joinWaitingList = '/join-waiting-list';
  static const String sellMySlot = '/sell-my-slot';
  static const String reward = '/reward';
  static const String rewardLifestyle = '/reward-lifestyle';
  static const String rewardLifestyleDetail = '/reward-lifestyle-detail';
  static const String rewardDetail = '/reward-detail';
  static const String selectCouponCode = '/select-coupon-code';
  static const String store = '/store';
  static const String productDetail = '/product-detail';
  static const String carListingDetail = '/car-listing-detail';
  static const String passengerForm = '/passenger-form';
  static const String addon = '/addon';
  static const String productPayment = '/product-payment';
  static const String orderStatus = '/order-status';
  static const String orderStatusDetail = '/order-status-detail';

  /// Check if a route has been migrated to go_router
  static bool isMigratedToGoRouter(String routeName) {
    return migratedRoutes.contains(routeName);
  }

  /// Get all migrated routes
  static Set<String> get migratedRoutes => {
        guest,
        login,
        profileEdit,
        navigationTest,
        register,
        forgotPasswordEmail,
        forgotPasswordCheckCode,
        forgotPasswordReset,
        socialLoginForm,
        verifyEmail,
        // Add more routes here as they get migrated
      };

  /// Get all GetX routes (routes not yet migrated)
  static Set<String> get getxRoutes => {
        home,
        bottombar,
        news,
        events,
        account,
        homeDetail,
        eventsDetail,
        booking,
        checkIn,
        bookingSuccess,
        eventsForm,
        eventsFormSuccess,
        eventsFormSigned,
        eventsFormUpdate,
        eventsFormUpdateSuccess,
        vehicles,
        vehiclesAdd,
        vehiclesEdit,
        kconfirmDialog,
        notification,
        notificationDetail,
        eventsFormSign,
        eventsFormSignSuccess,
        accountDelete,
        chatList,
        chat,
        leaderboard,
        personalResults,
        leaderboardDetail,
        forceUpgrade,
        perlaps,
        joinWaitingList,
        sellMySlot,
        reward,
        rewardLifestyle,
        rewardLifestyleDetail,
        rewardDetail,
        selectCouponCode,
        store,
        productDetail,
        carListingDetail,
        passengerForm,
        addon,
        productPayment,
        orderStatus,
        orderStatusDetail,
      };
}
