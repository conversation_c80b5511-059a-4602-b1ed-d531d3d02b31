import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

// Auth feature imports - using feature-based structure
import '../features/auth/login/views/login_view_hybrid.dart';
import '../features/auth/register/views/register_view_hybrid.dart';
import '../features/auth/forgot_password/views/forgot_password_email_view_hybrid.dart';
import '../features/auth/forgot_password/views/forgot_password_check_code_view_hybrid.dart';
import '../features/auth/forgot_password/views/forgot_password_reset_view_hybrid.dart';
import '../features/auth/social_form/views/social_login_form_view_hybrid.dart';
import '../features/auth/social_form/bindings/social_login_form_binding.dart';
import '../features/auth/verify_email/views/verify_email_view_hybrid.dart';
import '../features/auth/verify_email/bindings/verify_email_binding.dart';
import '../features/auth/guest/views/guest_view.dart';

// Non-auth imports
import '../modules/profile_edit/views/profile_edit_view.dart';
import '../modules/profile_edit/bindings/profile_edit_binding.dart';
import '../widgets/navigation_test_widget.dart';

/// go_router configuration for migrated routes
class GoRouterConfig {
  static final GlobalKey<NavigatorState> _rootNavigatorKey =
      GlobalKey<NavigatorState>();
  static final GlobalKey<NavigatorState> _shellNavigatorKey =
      GlobalKey<NavigatorState>();

  static GlobalKey<NavigatorState> get rootNavigatorKey => _rootNavigatorKey;
  static GlobalKey<NavigatorState> get shellNavigatorKey => _shellNavigatorKey;

  static GoRouter createRouter() {
    debugPrint('GO_ROUTER: Creating GoRouter configuration');
    debugPrint('GO_ROUTER: Initial location: /');
    debugPrint('GO_ROUTER: Debug diagnostics enabled: true');

    return GoRouter(
      navigatorKey: _rootNavigatorKey,
      initialLocation: '/',
      debugLogDiagnostics: true,
      onException: (context, state, router) {
        debugPrint('GO_ROUTER: Exception occurred - ${state.error}');
        debugPrint('GO_ROUTER: Current location: ${state.uri}');
      },
      routes: [
        // Login route - simple route to start migration
        GoRoute(
          path: '/login',
          name: 'login',
          builder: (context, state) {
            return const LoginViewHybrid();
          },
        ),

        // Register route
        GoRoute(
          path: '/register',
          name: 'register',
          builder: (context, state) {
            return const RegisterViewHybrid();
          },
        ),

        // Forgot Password Email route
        GoRoute(
          path: '/forgot-password-email',
          name: 'forgot-password-email',
          builder: (context, state) {
            return const ForgotPasswordEmailViewHybrid();
          },
        ),

        // Forgot Password Check Code route
        GoRoute(
          path: '/forgot-password-check-code',
          name: 'forgot-password-check-code',
          builder: (context, state) {
            return const ForgotPasswordCheckCodeViewHybrid();
          },
        ),

        // Forgot Password Reset route
        GoRoute(
          path: '/forgot-password-reset',
          name: 'forgot-password-reset',
          builder: (context, state) {
            return const ForgotPasswordResetViewHybrid();
          },
        ),

        // Profile Edit route - another simple route
        GoRoute(
          path: '/profile-edit',
          name: 'profile-edit',
          builder: (context, state) {
            // Initialize GetX binding for this route
            ProfileEditBinding().dependencies();
            return const ProfileEditView();
          },
        ),

        // Navigation test route
        GoRoute(
          path: '/navigation-test',
          name: 'navigation-test',
          builder: (context, state) {
            return const NavigationTestWidget();
          },
        ),

        // Social Login Form route
        GoRoute(
          path: '/social-login-form',
          name: 'social-login-form',
          builder: (context, state) {
            // Initialize GetX binding for this route
            SocialLoginFormBinding().dependencies();
            return const SocialLoginFormViewHybrid();
          },
        ),

        // Verify Email route
        GoRoute(
          path: '/verify-email',
          name: 'verify-email',
          builder: (context, state) {
            // Initialize GetX binding for this route
            VerifyEmailBinding().dependencies();
            return const VerifyEmailViewHybrid();
          },
        ),

        // Guest route
        GoRoute(
          path: '/guest',
          name: 'guest',
          builder: (context, state) {
            return const GuestView();
          },
        ),

        // Fallback route - redirects to GetX router
        GoRoute(
          path: '/',
          name: 'fallback',
          builder: (context, state) {
            // This should never be reached as we redirect to GetX
            return const Scaffold(
              body: Center(
                child: CircularProgressIndicator(),
              ),
            );
          },
        ),
      ],

      // Error handling
      errorBuilder: (context, state) {
        debugPrint('GO_ROUTER: Error occurred - ${state.error}');
        debugPrint('GO_ROUTER: Error location: ${state.uri}');
        debugPrint('GO_ROUTER: Error extra: ${state.extra}');

        return Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text('Route not found: ${state.uri}'),
                const SizedBox(height: 8),
                Text('Error: ${state.error}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => context.go('/'),
                  child: const Text('Go Home'),
                ),
              ],
            ),
          ),
        );
      },

      // Redirect logic
      redirect: (context, state) {
        final location = state.uri.path;
        debugPrint('GO_ROUTER: Redirect called for location: $location');

        // List of routes handled by go_router
        const goRouterRoutes = {
          '/login',
          '/register',
          '/forgot-password-email',
          '/forgot-password-check-code',
          '/forgot-password-reset',
          '/profile-edit',
          '/navigation-test',
          '/social-login-form',
          '/verify-email',
          '/guest',
        };

        debugPrint('GO_ROUTER: Available go_router routes: $goRouterRoutes');
        debugPrint(
            'GO_ROUTER: Is location in go_router routes: ${goRouterRoutes.contains(location)}');

        // If this route is not handled by go_router, let GetX handle it
        if (!goRouterRoutes.contains(location)) {
          debugPrint(
              'GO_ROUTER: Route $location not handled by go_router, letting it proceed');
          return null; // Let the route proceed normally
        }

        debugPrint(
            'GO_ROUTER: Route $location is handled by go_router, no redirect needed');
        return null; // No redirect needed
      },
    );
  }
}

/// Extension to get arguments from go_router state
extension GoRouterStateExtension on GoRouterState {
  /// Get arguments passed from GetX-style navigation
  dynamic get arguments {
    final extra = this.extra;
    if (extra is Map<String, dynamic> && extra.containsKey('arguments')) {
      return extra['arguments'];
    }
    return null;
  }
}
