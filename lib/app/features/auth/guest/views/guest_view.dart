import 'package:automoment/app/features/auth/login/views/login_view_hybrid.dart';
import 'package:automoment/app/features/auth/register/views/register_view_hybrid.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/guest_controller.dart';

class GuestView extends GetView<GuestController> {
  const GuestView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage("assets/images/launch_image.png"),
                fit: BoxFit
                    .cover //Image.asset("assets/images/launch_image.png"),
                )),
        child: Stack(
          children: [
            SafeArea(
              child: Column(
                children: [
                  const SizedBox(height: 60),
                  Center(
                      child: SizedBox(
                          height: 200,
                          width: 200,
                          child: Image.asset("assets/images/logobig.png"))),
                  const Expanded(
                    child: <PERSON><PERSON><PERSON><PERSON>(),
                  ),
                  // footer
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        ElevatedButton(
                            style: ButtonStyle(
                                foregroundColor: WidgetStateProperty.all<Color>(
                                    Colors.white),
                                backgroundColor: WidgetStateProperty.all<Color>(
                                    Colors.black),
                                shape: WidgetStateProperty.all<
                                        RoundedRectangleBorder>(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(18.0),
                                        side: const BorderSide(
                                            color: Colors.black)))),
                            onPressed: () {
                              showModalBottomSheet(
                                  isScrollControlled: true,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20.0),
                                  ),
                                  backgroundColor: Colors.white,
                                  builder: (context) {
                                    return const RegisterViewHybrid();
                                  },
                                  context: context);
                            },
                            child: Container(
                              padding:
                                  const EdgeInsets.only(left: 20, right: 20),
                              height: 50,
                              child: const Center(
                                child: Text("Register Now",
                                    style: TextStyle(fontSize: 16)),
                              ),
                            )),
                        const SizedBox(height: 10),
                        ElevatedButton(
                            style: ButtonStyle(
                                foregroundColor: WidgetStateProperty.all<Color>(
                                    Colors.black),
                                backgroundColor: WidgetStateProperty.all<Color>(
                                    Colors.white),
                                shape: WidgetStateProperty.all<
                                        RoundedRectangleBorder>(
                                    RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(18.0),
                                        side: const BorderSide(
                                            color: Colors.white)))),
                            onPressed: () {
                              // Option A: Keep modal approach with hybrid view
                              showModalBottomSheet(
                                  isScrollControlled: true,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20.0),
                                  ),
                                  backgroundColor: Colors.white,
                                  builder: (context) {
                                    return const LoginViewHybrid();
                                  },
                                  context: context);

                              // Option B: Use navigation service (uncomment to test)
                              // NavigationService.instance.toNamed(RouteNames.login);
                            },
                            child: Container(
                              padding:
                                  const EdgeInsets.only(left: 20, right: 20),
                              height: 50,
                              child: const Center(
                                child: Text("Login",
                                    style: TextStyle(fontSize: 16)),
                              ),
                            )),
                        const SizedBox(height: 10),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // Positioned(
            //   top: 10,
            //   right: 10,
            //   child: SafeArea(
            //     child: IconButton(
            //       onPressed: (){
            //         print("close");
            //         Get.back();
            //         //Session.shared.changeRootViewToDashBoard();
            //       },
            //       icon: const Icon(
            //         Icons.close,
            //         color: Colors.white,
            //         size: 30
            //       ),
            //     ),
            //   ),
            // ),
          ],
        ),
      ),
    );
  }
}
