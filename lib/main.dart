import 'dart:convert';
import 'dart:io';

import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:automoment/app/services/api_client.dart';
import 'package:automoment/app/services/deep_link_service.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'app/constants/app_config.dart';
import 'app/controllers/user_controller.dart';
import 'app/globals.dart';
import 'app/helpers/notification_util.dart';
import 'app/routes/app_pages.dart';
import 'app/routes/route_names.dart';
import 'app/services/analytics_service.dart';
import 'app/widgets/hybrid_app.dart';
import 'firebase_options.dart';

Future<void> main() async {
  // Add comprehensive debugging
  debugPrint('=== MAIN: Starting app initialization ===');

  if (!AppConfig.isProduction) {
    HttpOverrides.global = MyHttpOverrides();
    debugPrint('MAIN: HttpOverrides set for non-production');
  }

  debugPrint('MAIN: Initializing GetStorage...');
  await GetStorage.init();
  debugPrint('MAIN: GetStorage initialized successfully');

  // FIREBASE push notification setup

  debugPrint('MAIN: Ensuring widgets binding...');
  WidgetsFlutterBinding.ensureInitialized();
  debugPrint('MAIN: Widgets binding ensured');

  // Initialize Firebase first
  debugPrint('MAIN: Initializing Firebase...');
  FirebaseApp firebaseApp = await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  debugPrint('MAIN: Firebase initialized successfully');

  debugPrint('MAIN: Setting up Firebase Database...');
  firebaseDatabase = FirebaseDatabase.instanceFor(
      app: firebaseApp, databaseURL: AppConfig.firebaseDatabaseUrl);
  firebaseDatabase.setPersistenceEnabled(true);
  firebaseDatabase.setPersistenceCacheSizeBytes(10000000); // 10MB
  firebaseDatabase.setLoggingEnabled(
      !AppConfig.isProduction); // Enable logging only in debug/dev
  debugPrint('MAIN: Firebase Database configured');

  debugPrint('MAIN: Setting up Firebase Storage...');
  firebaseStorage = FirebaseStorage.instance;
  debugPrint('MAIN: Firebase Storage configured');

  // Initialize Firebase Analytics and register the service
  debugPrint('MAIN: Initializing Firebase Analytics...');
  FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  Get.put(AnalyticsService(), permanent: true);
  debugPrint('MAIN: Firebase Analytics and AnalyticsService initialized');

  debugPrint('MAIN: Setting up Firebase Messaging...');
  FirebaseMessaging messaging = FirebaseMessaging.instance;

  debugPrint('MAIN: Requesting notification permissions...');
  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );

  if (settings.authorizationStatus == AuthorizationStatus.authorized) {
    debugPrint('MAIN: User granted notification permission');
  } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
    debugPrint('MAIN: User granted provisional notification permission');
  } else {
    debugPrint(
        'MAIN: User declined or has not accepted notification permission');
  }

  // ANDROID Channel

  const AndroidNotificationChannel channel = AndroidNotificationChannel(
    'high_importance_channel', // id
    'High Importance Notifications', // title
    //'This channel is used for important notifications.', // description
    importance: Importance.max,
  );

  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // Initialize flutterLocalNotificationsPlugin once here
  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings(
          'ic_launcher'); // Ensure 'ic_launcher' is in android/app/src/main/res/mipmap
  const DarwinInitializationSettings initializationSettingsDarwin =
      DarwinInitializationSettings(
          onDidReceiveLocalNotification: onDidReceiveLocalNotification);
  const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsDarwin);
  await flutterLocalNotificationsPlugin.initialize(initializationSettings,
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse);

  await flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin>()
      ?.createNotificationChannel(channel);

  // IOS

  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: false, //true, // Required to display a heads up notification
    badge: true,
    sound: true,
  );

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // ANDROID foreground

  FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
    var type = message.data['type'];
    var id = message.data['data'];

    debugPrint("onMessage: $type : $id");
    RemoteNotification? notification = message.notification;
    AndroidNotification? android = message.notification?.android;

    // If `onMessage` is triggered with a notification, construct our own
    // local notification to show to users using the created channel.
    if (notification != null && android != null) {
      flutterLocalNotificationsPlugin.show(
          notification.hashCode,
          notification.title,
          notification.body,
          NotificationDetails(
            android: AndroidNotificationDetails(
              channel.id,
              channel.name,
              //channel.description,
              //icon: android?.smallIcon,
              icon: "@mipmap/ic_launcher",
              // other properties...
            ),
          ),
          payload: "${message.data['type']}|${message.data['data']}");
    }
  });

  //
  await AppTrackingTransparency.requestTrackingAuthorization();

  // Determine initial route based on app version, login status, and deep links
  Future<String> determineInitialRoute() async {
    debugPrint('=== MAIN: Determining initial route ===');

    // First check if app version is latest
    debugPrint('MAIN: Checking app version...');
    final isLatestVersion = await isAppLatestVersion();
    debugPrint('MAIN: App version check result: $isLatestVersion');

    if (!isLatestVersion) {
      debugPrint(
          'MAIN: App needs upgrade, routing to FORCE_UPGRADE: ${Routes.FORCE_UPGRADE}');
      return Routes.FORCE_UPGRADE;
    }

    // Check for user login status
    debugPrint('MAIN: Checking user login status...');
    final userController = Get.put(UserController());
    final isLoggedIn = userController.isUserLoggedIn();
    debugPrint('MAIN: User logged in: $isLoggedIn');

    // Check for deep links
    debugPrint('MAIN: Checking for deep links...');
    final deepLinkService = Get.find<DeepLinkService>();
    final pendingEventId = deepLinkService.getPendingEventId();
    debugPrint('MAIN: Pending event ID: $pendingEventId');

    if (pendingEventId != null && isLoggedIn) {
      debugPrint(
          'MAIN: Found pending event ID: $pendingEventId, preparing for navigation');
      // Store the event ID for navigation after app is initialized
      GetStorage().write('initial_event_id', pendingEventId);
      debugPrint('MAIN: Routing to BOTTOMBAR: ${Routes.BOTTOMBAR}');
      return Routes.BOTTOMBAR;
    }

    // Default routes based on login status
    final finalRoute = isLoggedIn ? Routes.BOTTOMBAR : Routes.GUEST;
    debugPrint('MAIN: Final route determined: $finalRoute');
    debugPrint(
        'MAIN: Route is migrated to go_router: ${RouteNames.isMigratedToGoRouter(finalRoute)}');
    return finalRoute;
  }

  //Uri.base.path;

  // final PendingDynamicLinkData? initialLink = await FirebaseDynamicLinks.instance.getInitialLink();
  // debugPrint("initialLink: ${initialLink?.android.toString()}");

  // Initialize DeepLinkService first
  debugPrint('MAIN: Initializing DeepLinkService...');
  await Get.putAsync(() => DeepLinkService().init(), permanent: true);
  debugPrint('MAIN: DeepLinkService initialized successfully');

  // Set up a callback to process any pending deep links after the app is initialized
  debugPrint('MAIN: Setting up AppInitializationController...');
  Get.put(AppInitializationController());
  debugPrint('MAIN: AppInitializationController set up');

  debugPrint('MAIN: Initializing Sentry...');
  await SentryFlutter.init(
    (options) {
      options.debug = false;
      options.dsn =
          'https://<EMAIL>/4507121265868800';
      options.tracesSampleRate = 1.0;
      options.profilesSampleRate = 1.0;
      options.experimental.replay.sessionSampleRate = 1.0;
      options.experimental.replay.onErrorSampleRate = 1.0;
      options.experimental.privacy.maskAllText = false;
      options.experimental.privacy.maskAllImages = false;
      options.experimental.privacy.mask<IconButton>();
      options.experimental.privacy.unmask<Image>();
      options.experimental.privacy.maskCallback<Text>(
          (Element element, Text widget) =>
              (widget.data?.contains('secret') ?? false)
                  ? SentryMaskingDecision.mask
                  : SentryMaskingDecision.continueProcessing);
    },
    appRunner: () async {
      debugPrint('MAIN: Sentry initialized, starting app runner...');
      final initialRoute = await determineInitialRoute();
      debugPrint('MAIN: Creating HybridApp with initialRoute: $initialRoute');

      return runApp(
        HybridApp(
          initialRoute: initialRoute,
          analytics: analytics,
        ),
      );
    },
  );

  debugPrint('=== MAIN: App initialization completed ===');
}

Future<bool> isAppLatestVersion() async {
  debugPrint('=== VERSION_CHECK: Starting app version check ===');

  try {
    debugPrint('VERSION_CHECK: Getting package info...');
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    int currentBuildVersion = int.tryParse(packageInfo.buildNumber) ?? 0;
    debugPrint('VERSION_CHECK: Current build version: $currentBuildVersion');
    debugPrint('VERSION_CHECK: App name: ${packageInfo.appName}');
    debugPrint('VERSION_CHECK: Package name: ${packageInfo.packageName}');
    debugPrint('VERSION_CHECK: Version: ${packageInfo.version}');

    debugPrint('VERSION_CHECK: Calling API to check latest versions...');
    String responseString = await ApiClient.checkAppVersions();
    debugPrint('VERSION_CHECK: API response: $responseString');

    Map<String, dynamic> versions = jsonDecode(responseString);
    debugPrint('VERSION_CHECK: Parsed versions: $versions');

    int latestAndroidVersion =
        int.tryParse(versions['android']?.toString() ?? '0') ?? 0;
    int latestIOSVersion =
        int.tryParse(versions['ios']?.toString() ?? '0') ?? 0;

    debugPrint('VERSION_CHECK: Current build version: $currentBuildVersion');
    debugPrint(
        'VERSION_CHECK: Latest Android version (latest | current): $latestAndroidVersion | $currentBuildVersion');
    debugPrint(
        'VERSION_CHECK: Latest iOS version (latest | current): $latestIOSVersion | $currentBuildVersion');

    bool isLatest;
    if (Platform.isAndroid) {
      isLatest = currentBuildVersion >= latestAndroidVersion;
      debugPrint(
          'VERSION_CHECK: Platform is Android, version is latest: $isLatest');
    } else if (Platform.isIOS) {
      isLatest = currentBuildVersion >= latestIOSVersion;
      debugPrint(
          'VERSION_CHECK: Platform is iOS, version is latest: $isLatest');
    } else {
      isLatest = true;
      debugPrint(
          'VERSION_CHECK: Platform is other, assuming latest: $isLatest');
    }

    debugPrint(
        '=== VERSION_CHECK: Version check completed, result: $isLatest ===');
    return isLatest;
  } catch (e, stackTrace) {
    // Log the error and decide on fallback behavior.
    // Returning true allows the app to proceed if the version check API fails.
    // This might be acceptable to avoid blocking users due to a transient API issue.
    debugPrint(
        'VERSION_CHECK: Error checking app version: $e. Assuming current version is acceptable.');
    debugPrint('VERSION_CHECK: Stack trace: $stackTrace');
    Sentry.captureException(e, stackTrace: stackTrace);
    debugPrint('=== VERSION_CHECK: Version check failed, returning true ===');
    return true;
  }
}

// void listenToNotification(FirebaseMessaging messaging) {
//   messaging.configure(
//     onMessage: (Map<String, dynamic> message) async {
//       debugPrint("onMessage: $message");
//     },
//     onLaunch: (Map<String, dynamic> message) async {
//       debugPrint("onLaunch: $message");
//     },
//     onResume: (Map<String, dynamic> message) async {
//       debugPrint("onResume: ${message["data"]}");
//       SchedulerBinding.instance.addPostFrameCallback((_) {
//         debugPrint("onResume message: $message");
//       });
//     },
//   );
// }

void onDidReceiveLocalNotification(
    int id, String? title, String? body, String? payload) {
  debugPrint("onDidReceiveLocalNotification");

  // split payload
  var type = payload?.split('|')[0];
  var id = payload?.split('|')[1];

  NotificationUtil().pushNotificationAction(type!, id!);
}

void onDidReceiveNotificationResponse(
    NotificationResponse notificationResponse) {
  debugPrint(
      "onDidReceiveNotificationResponse: ${notificationResponse.payload}");

  // split payload
  var payload = notificationResponse.payload;
  var type = payload?.split('|')[0];
  var id = payload?.split('|')[1];

  NotificationUtil().pushNotificationAction(type!, id!);
}

Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  await Firebase.initializeApp();

  var type = message.data['type'];
  var id = message.data['data'];

  debugPrint("Handling a background message: ${message.messageId}");
  debugPrint("Handling a background message: ${message.data}");
  debugPrint("Handling a background message: $type : $id");
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

// Controller to handle app initialization and process deep links
class AppInitializationController extends GetxController {
  final DeepLinkService _deepLinkService = Get.find<DeepLinkService>();

  @override
  void onReady() {
    super.onReady();
    // Process any pending deep links after the app is fully initialized
    debugPrint(
        'AppInitializationController: App is ready, processing pending deep links');
    debugPrint(
        'AppInitializationController: DeepLinkService instance: $_deepLinkService');
    //_deepLinkService.processPendingDeepLinks();
  }
}
